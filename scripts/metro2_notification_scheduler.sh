#!/bin/bash

# Metro2 Notification Scheduler Script for Cron Job
# This script runs the Metro2 notification processing in CLI mode

# Set script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# Set working directory to project root
cd "$PROJECT_DIR" || {
    echo "Error: Cannot change to project directory: $PROJECT_DIR"
    exit 1
}

# Set environment variables
export RMBASE_FILE_CFG="configs/local.ini"

# Set log file path
LOG_FILE="$PROJECT_DIR/logs/metro2_notification_scheduler.log"
LOG_DIR="$(dirname "$LOG_FILE")"

# Create log directory if it doesn't exist
mkdir -p "$LOG_DIR"

# Function to log messages
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# Function to check if the CLI binary exists
check_binary() {
    if [ ! -f "./build/bin/cli_metro2_notification" ]; then
        log_message "ERROR: CLI binary './build/bin/cli_metro2_notification' not found"
        log_message "Please build the CLI first with: cd src && go build -o ../build/bin/cli_metro2_notification ../bash/cli_metro2_notification.go"
        exit 1
    fi
}

# Function to run the Metro2 notification scheduler
run_scheduler() {
    log_message "Starting Metro2 notification processing..."

    # Run the scheduler from project directory
    output=$(./build/bin/cli_metro2_notification 2>&1)
    local exit_code=$?

    if [ $exit_code -eq 0 ]; then
        log_message "Metro2 notification processing completed successfully"
        log_message "CLI output: $output"
    else
        log_message "ERROR: Metro2 notification processing failed with exit code: $exit_code"
        log_message "CLI error output: $output"
        exit $exit_code
    fi
}

# Main execution
main() {
    log_message "=== Metro2 Notification Scheduler Cron Job Started ==="
    
    # Check if binary exists
    check_binary
    
    # Run the scheduler
    run_scheduler
    
    log_message "=== Metro2 Notification Scheduler Cron Job Completed ==="
}

# Execute main function
main "$@"
