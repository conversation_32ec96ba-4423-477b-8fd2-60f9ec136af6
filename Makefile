# ==========================================
# Rent Report Application Makefile
# ==========================================

# Variables
APP_NAME := rent_report
VERSION := $(shell git describe --tags --always --dirty 2>/dev/null || echo "dev")
BUILD_TIME := $(shell date -u '+%Y-%m-%d_%H:%M:%S')
GO_VERSION := $(shell go version | cut -d ' ' -f 3)

# Directories
BUILD_DIR := build
BIN_DIR := $(BUILD_DIR)/bin
SRC_DIR := src
WEB_DIR := $(SRC_DIR)/web
TESTS_DIR := tests
CONFIGS_DIR := configs
CLI_DIR := bash

# Go build parameters
LDFLAGS := -ldflags "-X main.Version=$(VERSION) -X main.BuildTime=$(BUILD_TIME) -X main.GoVersion=$(GO_VERSION)"
CGO_ENABLED := 0

# Target platforms for cross-compilation
PLATFORMS := linux/amd64 linux/arm64 darwin/amd64 darwin/arm64 windows/amd64

# Colors for output
RED := \033[0;31m
GREEN := \033[0;32m
YELLOW := \033[0;33m
BLUE := \033[0;34m
NC := \033[0m # No Color

# Default target
.DEFAULT_GOAL := help

# ==========================================
# Main Targets
# ==========================================

.PHONY: all help test build build-arm build-cli clean dev deps vendor lint docker web-build full-build

all: deps test build build-cli ## Build everything (deps, test, build, CLI tools)

help: ## Show this help message
	@echo "$(BLUE)Rent Report Application Makefile$(NC)"
	@echo "$(YELLOW)Available targets:$(NC)"
	@awk 'BEGIN {FS = ":.*##"} /^[a-zA-Z_-]+:.*##/ { printf "  $(GREEN)%-15s$(NC) %s\n", $$1, $$2 }' $(MAKEFILE_LIST)

# ==========================================
# Testing Targets
# ==========================================

test: ## Run all tests (both src and tests directory)
	@echo "$(BLUE)Running Go tests in src directory...$(NC)"
	@cd $(SRC_DIR) && go test -v ./...
	@echo "$(BLUE)Running integration tests in tests directory...$(NC)"
	@cd $(TESTS_DIR) && go test -v ./...

test-src: ## Run only src directory tests
	@echo "$(BLUE)Running Go tests in src directory...$(NC)"
	@cd $(SRC_DIR) && go test -v ./...

test-integration: ## Run only integration tests
	@echo "$(BLUE)Running integration tests in tests directory...$(NC)"
	@cd $(TESTS_DIR) && go test -v ./...

test-verbose: ## Run tests with verbose output and no cache
	@echo "$(BLUE)Running verbose tests...$(NC)"
	@cd $(SRC_DIR) && go test -v ./... -count=1
	@cd $(TESTS_DIR) && go test -v ./... -count=1

test-coverage: ## Generate test coverage report
	@echo "$(BLUE)Generating test coverage report...$(NC)"
	@cd $(SRC_DIR) && go test -v ./... -coverprofile=coverage.out
	@cd $(SRC_DIR) && go tool cover -html=coverage.out -o coverage.html
	@echo "$(GREEN)Coverage report generated: $(SRC_DIR)/coverage.html$(NC)"

test-scenarios: ## Run specific scenario tests
	@echo "$(BLUE)Running scenario tests...$(NC)"
	@cd $(TESTS_DIR) && go run test_scenario*.go

# ==========================================
# Build Targets
# ==========================================

build: ## Build for current platform
	@echo "$(BLUE)Building $(APP_NAME) for current platform...$(NC)"
	@mkdir -p $(BIN_DIR)
	@cd $(SRC_DIR) && CGO_ENABLED=$(CGO_ENABLED) go build $(LDFLAGS) -o ../$(BIN_DIR)/$(APP_NAME) .
	@echo "$(GREEN)Build completed: $(BIN_DIR)/$(APP_NAME)$(NC)"

build-arm: ## Build for ARM64 Linux
	@echo "$(BLUE)Building $(APP_NAME) for ARM64 Linux...$(NC)"
	@mkdir -p $(BIN_DIR)
	@cd $(SRC_DIR) && CGO_ENABLED=$(CGO_ENABLED) GOOS=linux GOARCH=arm64 go build $(LDFLAGS) -o ../$(BIN_DIR)/$(APP_NAME)-linux-arm64 .
	@echo "$(GREEN)ARM64 build completed: $(BIN_DIR)/$(APP_NAME)-linux-arm64$(NC)"

build-all: ## Build for all supported platforms
	@echo "$(BLUE)Building $(APP_NAME) for all platforms...$(NC)"
	@mkdir -p $(BIN_DIR)
	@$(foreach platform,$(PLATFORMS), \
		echo "Building for $(platform)..."; \
		cd $(SRC_DIR) && \
		GOOS=$(word 1,$(subst /, ,$(platform))) \
		GOARCH=$(word 2,$(subst /, ,$(platform))) \
		CGO_ENABLED=$(CGO_ENABLED) \
		go build $(LDFLAGS) -o ../$(BIN_DIR)/$(APP_NAME)-$(subst /,-,$(platform))$(if $(findstring windows,$(platform)),.exe) . ; \
	)
	@echo "$(GREEN)All platform builds completed$(NC)"

build-server: ## Build server binary for Docker deployment
	@echo "$(BLUE)Building server binary for Docker...$(NC)"
	@cd $(SRC_DIR) && CGO_ENABLED=$(CGO_ENABLED) GOOS=linux GOARCH=amd64 go build $(LDFLAGS) -o ../server .
	@echo "$(GREEN)Server binary built: server$(NC)"

build-cli: ## Build all CLI tools
	@echo "$(BLUE)Building CLI tools...$(NC)"
	@mkdir -p $(BIN_DIR)
	@cd $(SRC_DIR) && CGO_ENABLED=$(CGO_ENABLED) go build $(LDFLAGS) -o ../$(BIN_DIR)/cli_auto_payment ../$(CLI_DIR)/auto_payment/main.go
	@cd $(SRC_DIR) && CGO_ENABLED=$(CGO_ENABLED) go build $(LDFLAGS) -o ../$(BIN_DIR)/cli_debt_reminder ../$(CLI_DIR)/debt_reminder/main.go
	@cd $(SRC_DIR) && CGO_ENABLED=$(CGO_ENABLED) go build $(LDFLAGS) -o ../$(BIN_DIR)/cli_metro2_auto_generation ../$(CLI_DIR)/metro2_auto_generation/main.go
	@cd $(SRC_DIR) && CGO_ENABLED=$(CGO_ENABLED) go build $(LDFLAGS) -o ../$(BIN_DIR)/cli_metro2_notification ../$(CLI_DIR)/metro2_notification/main.go
	@cd $(SRC_DIR) && CGO_ENABLED=$(CGO_ENABLED) go build $(LDFLAGS) -o ../$(BIN_DIR)/cli_rent_report_batch ../$(CLI_DIR)/rent_report_batch/main.go
	@echo "$(GREEN)CLI tools built successfully$(NC)"

# ==========================================
# Frontend Targets
# ==========================================

web-deps: ## Install frontend dependencies
	@echo "$(BLUE)Installing frontend dependencies...$(NC)"
	@cd $(WEB_DIR) && npm install || (echo "$(RED)npm install failed, trying to fix...$(NC)" && rm -rf node_modules package-lock.json && npm install)
	@echo "$(GREEN)Frontend dependencies installed$(NC)"

web-build: web-deps ## Build frontend assets
	@echo "$(BLUE)Building frontend assets...$(NC)"
	@cd $(WEB_DIR) && npm run build
	@echo "$(GREEN)Frontend build completed$(NC)"

web-dev: ## Start frontend development server
	@echo "$(BLUE)Starting frontend development server...$(NC)"
	@cd $(WEB_DIR) && npm run dev

# ==========================================
# Copy Build Assets
# ==========================================

copy-web-assets: web-build ## Copy frontend build assets to build directory (now built directly to build/web/dist)
	@echo "$(BLUE)Frontend assets already built to $(BUILD_DIR)/web/dist$(NC)"
	@echo "$(GREEN)Frontend assets ready$(NC)"

copy-configs: ## Copy configuration files to build directory
	@echo "$(BLUE)Copying configuration files...$(NC)"
	@mkdir -p $(BUILD_DIR)/configs
	@cp -r $(CONFIGS_DIR)/* $(BUILD_DIR)/configs/
	@echo "$(GREEN)Configuration files copied$(NC)"

copy-docs: ## Copy documentation to build directory
	@echo "$(BLUE)Copying documentation...$(NC)"
	@mkdir -p $(BUILD_DIR)/docs
	@cp -r docs/* $(BUILD_DIR)/docs/
	@echo "$(GREEN)Documentation copied$(NC)"

# ==========================================
# Full Build Targets
# ==========================================

full-build: deps web-build build build-cli copy-web-assets copy-configs copy-docs ## Complete build (deps, frontend, backend, CLI tools, assets)
	@echo "$(GREEN)Full build completed successfully$(NC)"

full-build-arm: deps web-build build-arm build-cli copy-web-assets copy-configs copy-docs ## Complete ARM build with CLI tools and assets
	@echo "$(GREEN)Full ARM build completed successfully$(NC)"

# ==========================================
# Development Targets
# ==========================================

deps: ## Install all dependencies (Go and Node.js)
	@echo "$(BLUE)Installing Go dependencies...$(NC)"
	@cd $(SRC_DIR) && go mod download
	@cd $(SRC_DIR) && go mod tidy
	@echo "$(BLUE)Installing Node.js dependencies...$(NC)"
	@cd $(WEB_DIR) && npm install
	@echo "$(GREEN)All dependencies installed$(NC)"

vendor: ## Create vendor directory for Go dependencies
	@echo "$(BLUE)Creating vendor directory...$(NC)"
	@cd $(SRC_DIR) && go mod vendor
	@echo "$(GREEN)Vendor directory created$(NC)"

dev: web-build ## Start development environment (correct way)
	@echo "$(BLUE)Starting development environment...$(NC)"
	@echo "$(YELLOW)Building frontend first...$(NC)"
	@echo "$(YELLOW)Backend will start on port 8089$(NC)"
	@echo "$(YELLOW)Access application at: http://localhost:8089$(NC)"
	@cd $(SRC_DIR) && go run main.go --config ../configs/local.ini

dev-backend: ## Start only backend in development mode
	@echo "$(BLUE)Starting backend development server...$(NC)"
	@echo "$(YELLOW)Make sure frontend is built first with: make web-build$(NC)"
	@cd $(SRC_DIR) && go run main.go --config ../configs/local.ini

dev-frontend: ## Start frontend development server only
	@echo "$(BLUE)Starting frontend development server...$(NC)"
	@echo "$(YELLOW)Frontend dev server will start on port 5173$(NC)"
	@cd $(WEB_DIR) && npm run dev

dev-proxy: ## Start proxy server for development
	@echo "$(BLUE)Starting proxy server...$(NC)"
	@npm run dev

dev-full: ## Start both frontend dev server and backend (parallel)
	@echo "$(BLUE)Starting full development environment...$(NC)"
	@echo "$(YELLOW)Frontend dev server: http://localhost:5173$(NC)"
	@echo "$(YELLOW)Backend server: http://localhost:8089$(NC)"
	@trap 'kill %1; kill %2' INT; \
	(cd $(WEB_DIR) && npm run dev) & \
	(cd $(SRC_DIR) && go run main.go --config ../configs/local.ini) & \
	wait

run: build ## Build and run the application
	@echo "$(BLUE)Running $(APP_NAME)...$(NC)"
	@$(BIN_DIR)/$(APP_NAME) --config configs/local.ini

run-prod: build ## Run application in production mode
	@echo "$(BLUE)Running $(APP_NAME) in production mode...$(NC)"
	@GIN_MODE=release $(BIN_DIR)/$(APP_NAME) --config configs/local.ini

# ==========================================
# Code Quality Targets
# ==========================================

lint: ## Run code quality checks
	@echo "$(BLUE)Running code quality checks...$(NC)"
	@cd $(SRC_DIR) && go fmt ./...
	@cd $(SRC_DIR) && go vet ./...
	@echo "$(GREEN)Code quality checks completed$(NC)"

fmt: ## Format Go code
	@echo "$(BLUE)Formatting Go code...$(NC)"
	@cd $(SRC_DIR) && go fmt ./...
	@echo "$(GREEN)Code formatting completed$(NC)"

vet: ## Run Go vet
	@echo "$(BLUE)Running Go vet...$(NC)"
	@cd $(SRC_DIR) && go vet ./...
	@echo "$(GREEN)Go vet completed$(NC)"

# ==========================================
# Docker Targets
# ==========================================

create-dockerfile: ## Create Dockerfile in build directory
	@echo "$(BLUE)Creating Dockerfile for build directory...$(NC)"
	@echo 'FROM alpine:latest' > $(BUILD_DIR)/Dockerfile
	@echo 'RUN apk --no-cache add ca-certificates tzdata' >> $(BUILD_DIR)/Dockerfile
	@echo 'RUN addgroup -S appgroup' >> $(BUILD_DIR)/Dockerfile
	@echo 'RUN adduser -S -u 1000 -G appgroup appuser' >> $(BUILD_DIR)/Dockerfile
	@echo 'WORKDIR /app' >> $(BUILD_DIR)/Dockerfile
	@echo 'RUN mkdir -p /app/configs /app/web/dist /app/logs /app/docs' >> $(BUILD_DIR)/Dockerfile
	@echo 'RUN chown -R appuser:appgroup /app' >> $(BUILD_DIR)/Dockerfile
	@echo 'COPY bin/rent_report /app/server' >> $(BUILD_DIR)/Dockerfile
	@echo 'COPY web/dist/ /app/web/dist/' >> $(BUILD_DIR)/Dockerfile
	@echo 'COPY configs/ /app/configs/' >> $(BUILD_DIR)/Dockerfile
	@echo 'COPY docs/ /app/docs/' >> $(BUILD_DIR)/Dockerfile
	@echo 'RUN chown -R appuser:appgroup /app' >> $(BUILD_DIR)/Dockerfile
	@echo 'RUN chmod +x /app/server' >> $(BUILD_DIR)/Dockerfile
	@echo 'USER appuser' >> $(BUILD_DIR)/Dockerfile
	@echo 'EXPOSE 8089' >> $(BUILD_DIR)/Dockerfile
	@echo 'HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 CMD wget --no-verbose --tries=1 --spider http://localhost:8089/health || exit 1' >> $(BUILD_DIR)/Dockerfile
	@echo 'CMD ["/app/server", "--config", "/app/configs/local.ini"]' >> $(BUILD_DIR)/Dockerfile
	@echo "$(GREEN)Dockerfile created in build directory$(NC)"

docker-build-server: ## Build server binary for Docker (deprecated - use full-build instead)
	@echo "$(YELLOW)Warning: docker-build-server is deprecated. Use 'make full-build' instead.$(NC)"
	@echo "$(BLUE)Building server binary for Docker deployment...$(NC)"
	@cd $(SRC_DIR) && CGO_ENABLED=$(CGO_ENABLED) GOOS=linux GOARCH=amd64 go build $(LDFLAGS) -o ../$(BUILD_DIR)/bin/rent_report main.go
	@echo "$(GREEN)Server binary built: $(BUILD_DIR)/bin/rent_report$(NC)"

docker: full-build create-dockerfile ## Build Docker image with podman using build directory
	@echo "$(BLUE)Building Docker image with podman...$(NC)"
	@cd $(BUILD_DIR) && podman build -t rent_report:$(VERSION) .
	@echo "$(GREEN)Docker image built: rent_report:$(VERSION)$(NC)"

docker-latest: full-build create-dockerfile ## Build Docker image with latest tag using build directory
	@echo "$(BLUE)Building Docker image with latest tag...$(NC)"
	@cd $(BUILD_DIR) && docker build -t rent_report:latest .
	@echo "$(GREEN)Docker image built: rent_report:latest$(NC)"

docker-arm: web-build ## Build ARM Docker image with podman
	@echo "$(BLUE)Building ARM Docker image with podman...$(NC)"
	@podman build -f DockerfileArm -t rent_report_arm:$(VERSION) .
	@echo "$(GREEN)ARM Docker image built: rent_report_arm:$(VERSION)$(NC)"

docker-run: ## Run Docker container with podman
	@echo "$(BLUE)Running Docker container with podman...$(NC)"
	@echo "$(YELLOW)Container will be available at: http://localhost:8089$(NC)"
	@podman run -d --name rent_report_container \
		-v $(PWD)/$(CONFIGS_DIR):/app/configs \
		-p 8089:8089 \
		rent_report:latest
	@echo "$(GREEN)Docker container started$(NC)"

docker-run-interactive: ## Run Docker container interactively
	@echo "$(BLUE)Running Docker container interactively...$(NC)"
	@podman run -it --rm \
		-v $(PWD)/$(CONFIGS_DIR):/app/configs \
		-p 8089:8089 \
		rent_report:latest

docker-stop: ## Stop Docker container
	@echo "$(BLUE)Stopping Docker container...$(NC)"
	@podman stop rent_report_container || true
	@podman rm rent_report_container || true
	@echo "$(GREEN)Docker container stopped$(NC)"

docker-logs: ## Show Docker container logs
	@echo "$(BLUE)Showing Docker container logs...$(NC)"
	@podman logs -f rent_report_container

docker-shell: ## Get shell access to running container
	@echo "$(BLUE)Accessing container shell...$(NC)"
	@podman exec -it rent_report_container /bin/sh

# ==========================================
# Deployment Targets
# ==========================================

deploy-build: ## Build for deployment using build.sh
	@echo "$(BLUE)Running deployment build script...$(NC)"
	@chmod +x build.sh && ./build.sh
	@echo "$(GREEN)Deployment build completed$(NC)"

deploy-static: ## Deploy static files
	@echo "$(BLUE)Deploying static files...$(NC)"
	@chmod +x deploy-static.sh && ./deploy-static.sh
	@echo "$(GREEN)Static files deployed$(NC)"

# ==========================================
# Utility Targets
# ==========================================

clean: ## Clean build files and caches
	@echo "$(BLUE)Cleaning build files...$(NC)"
	@cd $(SRC_DIR) && go clean
	@rm -rf $(BUILD_DIR)
	@cd $(SRC_DIR) && rm -rf vendor
	@rm -f server
	@cd $(SRC_DIR) && rm -f coverage.out coverage.html
	@cd $(WEB_DIR) && rm -rf dist node_modules
	@echo "$(GREEN)Clean completed$(NC)"

clean-cache: ## Clean Go module cache
	@echo "$(BLUE)Cleaning Go module cache...$(NC)"
	@cd $(SRC_DIR) && go clean -modcache
	@echo "$(GREEN)Go module cache cleaned$(NC)"

info: ## Show build information
	@echo "$(BLUE)Build Information:$(NC)"
	@echo "  App Name: $(APP_NAME)"
	@echo "  Version: $(VERSION)"
	@echo "  Build Time: $(BUILD_TIME)"
	@echo "  Go Version: $(GO_VERSION)"
	@echo "  Build Dir: $(BUILD_DIR)"
	@echo "  Web Dir: $(WEB_DIR)"
	@echo "  Tests Dir: $(TESTS_DIR)"

logs: ## Show application logs
	@echo "$(BLUE)Showing application logs...$(NC)"
	@tail -f logs/*.log

# ==========================================
# Quick Commands & Workflows
# ==========================================

quick-test: ## Quick test run (no verbose output)
	@cd $(SRC_DIR) && go test ./... && cd ../$(TESTS_DIR) && go test ./...

quick-build: ## Quick build without dependencies
	@cd $(SRC_DIR) && CGO_ENABLED=$(CGO_ENABLED) go build $(LDFLAGS) -o ../$(BIN_DIR)/$(APP_NAME) .

quick-start: ## Quick start for development (build frontend + run backend)
	@echo "$(BLUE)Quick start: Building frontend and starting backend...$(NC)"
	@cd $(WEB_DIR) && npm run build
	@echo "$(GREEN)Frontend built, starting backend...$(NC)"
	@cd $(SRC_DIR) && go run main.go --config ../configs/local.ini

# ==========================================
# Workflow Commands (Common Development Tasks)
# ==========================================

start: ## Start application the correct way (recommended)
	@echo "$(BLUE)Starting Rent Report Application...$(NC)"
	@echo "$(YELLOW)Step 1: Building frontend assets...$(NC)"
	@cd $(WEB_DIR) && npm install && npm run build
	@echo "$(YELLOW)Step 2: Starting backend server...$(NC)"
	@echo "$(GREEN)Application will be available at: http://localhost:8089$(NC)"
	@cd $(SRC_DIR) && go run main.go --config ../configs/local.ini

docker-start: ## Complete Docker workflow (build + run)
	@echo "$(BLUE)Complete Docker workflow...$(NC)"
	@$(MAKE) docker-latest
	@$(MAKE) docker-run
	@echo "$(GREEN)Docker container is running at: http://localhost:8089$(NC)"

docker-restart: ## Restart Docker container
	@echo "$(BLUE)Restarting Docker container...$(NC)"
	@$(MAKE) docker-stop
	@$(MAKE) docker-run

docker-rebuild: ## Rebuild and restart Docker container
	@echo "$(BLUE)Rebuilding and restarting Docker container...$(NC)"
	@$(MAKE) docker-stop
	@$(MAKE) docker-latest
	@$(MAKE) docker-run

# ==========================================
# Development Workflow Helpers
# ==========================================

setup: ## Initial project setup
	@echo "$(BLUE)Setting up Rent Report project...$(NC)"
	@echo "$(YELLOW)Installing Go dependencies...$(NC)"
	@cd $(SRC_DIR) && go mod download && go mod tidy
	@echo "$(YELLOW)Creating vendor directory...$(NC)"
	@cd $(SRC_DIR) && go mod vendor
	@echo "$(YELLOW)Installing Node.js dependencies...$(NC)"
	@cd $(WEB_DIR) && npm install
	@echo "$(YELLOW)Building frontend assets...$(NC)"
	@cd $(WEB_DIR) && npm run build
	@echo "$(YELLOW)Building CLI tools...$(NC)"
	@$(MAKE) build-cli
	@echo "$(GREEN)Project setup completed!$(NC)"
	@echo "$(GREEN)Run 'make start' to start the application$(NC)"

reset: ## Reset project (clean + setup)
	@echo "$(BLUE)Resetting project...$(NC)"
	@$(MAKE) clean
	@$(MAKE) setup

# ==========================================
# API Documentation Targets
# ==========================================

docs-install: ## Install API documentation tools
	@echo "$(BLUE)Installing API documentation tools...$(NC)"
	@command -v redoc-cli >/dev/null 2>&1 || { \
		echo "$(YELLOW)Installing @redocly/cli (modern replacement for redoc-cli)...$(NC)"; \
		npm install -g @redocly/cli; \
	}
	@echo "$(GREEN)API documentation tools installed$(NC)"

docs-generate: ## Generate API documentation from OpenAPI spec
	@echo "$(BLUE)Generating API documentation...$(NC)"
	@mkdir -p docs/api
	@echo "$(YELLOW)Generating HTML documentation with Redocly...$(NC)"
	@cd $(SRC_DIR) && npx @redocly/cli build-docs api/openapi.yaml --output ../docs/api/index.html --title "Rent Report API Documentation" || \
	npx redoc-cli build api/openapi.yaml --output ../docs/api/index.html --title "Rent Report API Documentation"
	@echo "$(GREEN)API documentation generated: docs/api/index.html$(NC)"

docs-serve: docs-generate ## Generate and serve API documentation locally
	@echo "$(BLUE)Starting API documentation server...$(NC)"
	@echo "$(GREEN)API documentation available at: http://localhost:8080$(NC)"
	@echo "$(YELLOW)Press Ctrl+C to stop the server$(NC)"
	@cd docs/api && python3 -m http.server 8080 2>/dev/null || python -m SimpleHTTPServer 8080

docs-validate: ## Validate OpenAPI specification
	@echo "$(BLUE)Validating OpenAPI specification...$(NC)"
	@cd $(SRC_DIR) && npx @redocly/cli lint api/openapi.yaml || npx swagger-parser validate api/openapi.yaml
	@echo "$(GREEN)OpenAPI specification is valid$(NC)"

docs-clean: ## Clean generated documentation
	@echo "$(BLUE)Cleaning generated documentation...$(NC)"
	@rm -rf docs/api
	@echo "$(GREEN)Documentation cleaned$(NC)"

docs: docs-generate ## Alias for docs-generate
	@echo "$(GREEN)Use 'make docs-serve' to view the documentation$(NC)"

# ==========================================
# Validation Targets
# ==========================================

validate: lint test ## Validate code (lint + test)
	@echo "$(GREEN)Code validation completed successfully$(NC)"

pre-commit: fmt lint test ## Pre-commit checks
	@echo "$(GREEN)Pre-commit checks completed successfully$(NC)"
