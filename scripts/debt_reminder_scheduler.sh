#!/bin/bash

# Debt Reminder Scheduler Script
# This script runs the debt reminder CLI program
# Designed to be executed by cron on the 15th of each month

# Set script directory and paths
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
CLI_BINARY="$PROJECT_ROOT/build/bin/cli_debt_reminder"
LOG_FILE="$PROJECT_ROOT/logs/debt_reminder_scheduler.log"
PID_FILE="$PROJECT_ROOT/tmp/debt_reminder.pid"

# Create necessary directories
mkdir -p "$PROJECT_ROOT/logs"
mkdir -p "$PROJECT_ROOT/tmp"

# Function to log messages
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Function to cleanup on exit
cleanup() {
    if [ -f "$PID_FILE" ]; then
        rm -f "$PID_FILE"
    fi
}

# Set trap for cleanup
trap cleanup EXIT

# Check if another instance is running
if [ -f "$PID_FILE" ]; then
    OLD_PID=$(cat "$PID_FILE")
    if kill -0 "$OLD_PID" 2>/dev/null; then
        log_message "ERROR: Another debt reminder process is already running (PID: $OLD_PID)"
        exit 1
    else
        log_message "WARNING: Stale PID file found, removing it"
        rm -f "$PID_FILE"
    fi
fi

# Write current PID
echo $$ > "$PID_FILE"

log_message "=== Debt Reminder Scheduler Started ==="
log_message "Script: $0"
log_message "PID: $$"
log_message "Project Root: $PROJECT_ROOT"
log_message "CLI Binary: $CLI_BINARY"
log_message "Log File: $LOG_FILE"

# Check if CLI binary exists
if [ ! -f "$CLI_BINARY" ]; then
    log_message "ERROR: CLI binary not found at $CLI_BINARY"
    log_message "Please compile the CLI program first:"
    log_message "  cd $PROJECT_ROOT/src && go build -o ../build/bin/cli_debt_reminder ../bash/cli_debt_reminder.go"
    exit 1
fi

# Check if CLI binary is executable
if [ ! -x "$CLI_BINARY" ]; then
    log_message "WARNING: CLI binary is not executable, making it executable"
    chmod +x "$CLI_BINARY"
fi

# Change to project root directory
cd "$PROJECT_ROOT" || {
    log_message "ERROR: Failed to change to project root directory: $PROJECT_ROOT"
    exit 1
}

# Set environment variables
export RMBASE_FILE_CFG="configs/local.ini"

log_message "Environment variables set:"
log_message "  RMBASE_FILE_CFG=$RMBASE_FILE_CFG"

# Execute the CLI program
log_message "Executing debt reminder CLI program..."
START_TIME=$(date +%s)

# Run the CLI program from project root directory
if "$CLI_BINARY" 2>&1 | tee -a "$LOG_FILE"; then
    END_TIME=$(date +%s)
    DURATION=$((END_TIME - START_TIME))
    log_message "SUCCESS: Debt reminder completed successfully in ${DURATION} seconds"
    EXIT_CODE=0
else
    END_TIME=$(date +%s)
    DURATION=$((END_TIME - START_TIME))
    log_message "ERROR: Debt reminder failed after ${DURATION} seconds"
    EXIT_CODE=1
fi

log_message "=== Debt Reminder Scheduler Finished ==="

# Keep log files for 30 days, clean up older ones
find "$PROJECT_ROOT/logs" -name "debt_reminder_scheduler.log" -mtime +30 -delete 2>/dev/null || true

exit $EXIT_CODE
