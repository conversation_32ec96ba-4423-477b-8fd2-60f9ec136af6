#!/bin/bash

# Metro2 Auto Generation Script for Cron Job
# This script replaces the Go-based StartMetro2AutoGenerationScheduler()
# It should be run monthly on the 21st at 9:00 AM

# Set script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# Set working directory to project root
cd "$PROJECT_DIR" || {
    echo "Error: Cannot change to project directory: $PROJECT_DIR"
    exit 1
}

# Set environment variables
export RMBASE_FILE_CFG="configs/local.ini"

# Set log file path
LOG_FILE="$PROJECT_DIR/logs/metro2_auto_generation.log"
LOG_DIR="$(dirname "$LOG_FILE")"

# Create log directory if it doesn't exist
mkdir -p "$LOG_DIR"

# Function to log messages
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S UTC') - $1" | tee -a "$LOG_FILE"
}

# Function to check if CLI binary exists
check_binary() {
    if [ ! -f "./build/bin/cli_metro2_auto_generation" ]; then
        log_message "ERROR: CLI binary './build/bin/cli_metro2_auto_generation' not found"
        log_message "Please build the CLI first with: cd src && go build -o ../build/bin/cli_metro2_auto_generation ../bash/cli_metro2_auto_generation.go"
        exit 1
    fi
}

# Function to check if already generated this month
check_already_generated() {
    local current_month=$(date -u '+%Y-%m')
    
    # Query MongoDB to check if auto-generation already happened this month
    local count=$(mongosh rr --quiet --eval "
        var currentMonth = '$current_month';
        var count = db.metro2_generation_logs.countDocuments({
            reportMonth: currentMonth,
            fileName: { \$regex: /Metro2-Auto-Generated/ }
        });
        print(count);
    " 2>/dev/null | tail -1)

    # Ensure count is a valid number, default to 0 if empty or invalid
    if [ -z "$count" ] || ! [[ "$count" =~ ^[0-9]+$ ]]; then
        count=0
    fi

    if [ "$count" -gt 0 ]; then
        log_message "Metro2 auto-generation already completed for month $current_month (found $count records)"
        log_message "Skipping generation to prevent duplicates"
        exit 0
    fi
    
    log_message "No auto-generation found for month $current_month, proceeding..."
}

# Function to run Metro2 auto generation
run_auto_generation() {
    log_message "Starting Metro2 auto-generation process..."
    
    # Run the auto generation CLI from project directory
    output=$(./build/bin/cli_metro2_auto_generation 2>&1)
    local exit_code=$?
    
    if [ $exit_code -eq 0 ]; then
        log_message "Metro2 auto-generation completed successfully"
        log_message "CLI output: $output"
        
        # Extract generation log ID from output if available
        local generation_id=$(echo "$output" | grep -o "Generation ID: [a-zA-Z0-9]*" | cut -d' ' -f3)
        if [ -n "$generation_id" ]; then
            log_message "Generated Metro2 report with ID: $generation_id"
        fi
        
    else
        log_message "ERROR: Metro2 auto-generation failed with exit code: $exit_code"
        log_message "CLI error output: $output"
        exit $exit_code
    fi
}

# Function to verify generation results
verify_generation() {
    local current_month=$(date -u '+%Y-%m')
    
    # Check if generation was successful
    local latest_log=$(mongosh rr --quiet --eval "
        var currentMonth = '$current_month';
        var log = db.metro2_generation_logs.findOne(
            { 
                reportMonth: currentMonth,
                fileName: { \$regex: /Metro2-Auto-Generated/ }
            },
            { sort: { generatedAt: -1 } }
        );
        if (log) {
            print('SUCCESS: Generated ' + log.totalLeases + ' leases, ' + log.totalTenants + ' tenants');
        } else {
            print('ERROR: No generation log found');
        }
    " 2>/dev/null | tail -1)
    
    log_message "Generation verification: $latest_log"
    
    if [[ "$latest_log" == ERROR* ]]; then
        log_message "ERROR: Generation verification failed"
        exit 1
    fi
}

# Main execution
main() {
    log_message "=== Metro2 Auto Generation Cron Job Started ==="
    log_message "Current UTC time: $(date -u '+%Y-%m-%d %H:%M:%S')"
    log_message "Target schedule: Monthly on 21st at 09:00 UTC"
    
    # Check if binary exists
    check_binary
    
    # Check if already generated this month
    check_already_generated
    
    # Run auto generation
    run_auto_generation
    
    # Verify results
    verify_generation
    
    log_message "=== Metro2 Auto Generation Cron Job Completed ==="
}

# Execute main function
main "$@"
