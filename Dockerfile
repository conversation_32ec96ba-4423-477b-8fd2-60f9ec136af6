FROM alpine:latest
RUN addgroup -S appgroup
RUN adduser -S -u 1000 -G appgroup appuser
WORKDIR /app
RUN mkdir -p /app/uploads /app/configs /app/web/dist /app/logs /app/docs
RUN chown -R appuser:appgroup /app
COPY bin/rent_report /app/server
COPY web/dist/ /app/web/dist/
COPY configs/ /app/configs/
COPY docs/ /app/docs/
RUN chown -R appuser:appgroup /app
RUN chmod +x /app/server
USER appuser
EXPOSE 8089
CMD ["/app/server"]
