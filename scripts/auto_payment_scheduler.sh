#!/bin/bash

# Auto Payment Scheduler Script for Cron Job
# This script runs the auto payment scheduler in CLI mode
# Usage: ./auto_payment_scheduler.sh [-stdout]
#   -stdout: Output to stdout/stderr instead of log file

# Parse command line arguments
STDOUT_MODE=false
if [[ "$1" == "-stdout" ]]; then
    STDOUT_MODE=true
fi

# Set script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# Set working directory to project root
cd "$PROJECT_DIR" || {
    echo "Error: Cannot change to project directory: $PROJECT_DIR"
    exit 1
}

# Set environment variables (only if not already set)
export RMBASE_FILE_CFG="${RMBASE_FILE_CFG:-configs/local.ini}"

# Set log file path (only if not in stdout mode)
if [[ "$STDOUT_MODE" == "false" ]]; then
    LOG_FILE="$PROJECT_DIR/logs/auto_payment_scheduler.log"
    LOG_DIR="${LOG_DIR:-$(dirname "$LOG_FILE")}"

    # Create log directory if it doesn't exist
    mkdir -p "$LOG_DIR"
fi

# Function to log messages
log_message() {
    local message="$(date '+%Y-%m-%d %H:%M:%S') - $1"
    if [[ "$STDOUT_MODE" == "true" ]]; then
        echo "$message"
    else
        echo "$message" | tee -a "$LOG_FILE"
    fi
}

# Function to log error messages (always to stderr in stdout mode)
log_error() {
    local message="$(date '+%Y-%m-%d %H:%M:%S') - ERROR: $1"
    if [[ "$STDOUT_MODE" == "true" ]]; then
        echo "$message" >&2
    else
        echo "$message" | tee -a "$LOG_FILE"
    fi
}

# Function to check if the application binary exists
check_binary() {
    if [ ! -f "./build/bin/cli_auto_payment" ]; then
        log_error "CLI binary './build/bin/cli_auto_payment' not found"
        log_error "Please build the CLI first with: cd src && go build -o ../build/bin/cli_auto_payment ../bash/cli_auto_payment.go"
        exit 1
    fi
}

# Function to run the auto payment scheduler
run_scheduler() {
    log_message "Starting auto payment scheduler..."

    # Run the scheduler from project directory
    if [[ "$STDOUT_MODE" == "true" ]]; then
        # In stdout mode, let the CLI output directly to stdout/stderr
        ./build/bin/cli_auto_payment
    else
        # In file mode, capture output and log it
        ./build/bin/cli_auto_payment 2>&1 | while IFS= read -r line; do
            log_message "CLI: $line"
        done
    fi
    local exit_code=$?

    if [ $exit_code -eq 0 ]; then
        log_message "Auto payment scheduler completed successfully"
    else
        log_error "Auto payment scheduler failed with exit code: $exit_code"
        exit $exit_code
    fi
}

# Main execution
main() {
    log_message "=== Auto Payment Scheduler Cron Job Started ==="
    
    # Check if binary exists
    check_binary
    
    # Run the scheduler
    run_scheduler
    
    log_message "=== Auto Payment Scheduler Cron Job Completed ==="
}

# Execute main function
main "$@"
